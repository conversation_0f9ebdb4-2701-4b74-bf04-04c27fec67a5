# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

这是一个基于 DrissionPage 的抖音自动化脚本研究项目，用于教育和研究目的。项目演示了web自动化的基本机制，帮助理解和防范潜在的自动化滥用行为。

## 核心依赖

- **DrissionPage**: 用于浏览器自动化操作的Python库
- **time**: 标准库，用于延时控制

## 运行环境

- **系统**: Windows
- **终端**: PowerShell 7
- **Python环境**: Anaconda3 base环境
- **浏览器**: Chrome/Chromium

## 常用命令

```powershell
# 运行主程序
python main.py

# 安装依赖（如果需要）
pip install DrissionPage
```

## 代码架构

项目采用单文件结构，包含以下核心组件：

### 主要函数模块

- `find_dm_ele_click()`: 定位并点击私信按钮
- `find_dm_input_click()`: 定位输入框并发送消息
- `Chrom_sets()`: 浏览器配置设置
- `base_sets()`: 基础参数配置
- `main()`: 主执行流程

### 执行流程

1. **初始化阶段**: 配置浏览器选项和基础参数
2. **导航阶段**: 打开目标用户主页
3. **交互阶段**: 自动点击私信按钮
4. **消息发送**: 定位输入框并发送预设消息

## 安全注意事项

- 项目仅用于教育研究目的
- 实际使用时需遵守平台服务条款
- 建议在测试环境中运行
- 避免频繁自动化操作以防被检测

## 配置说明

关键配置参数在 `base_sets()` 函数中：

- `time_out`: 页面加载等待时间（默认5秒）
- `send_interval`: 消息发送间隔（默认1秒）
- `sec_uid`: 目标用户的唯一标识
- `message`: 要发送的消息内容

## 开发建议

- 修改配置参数时注意延时设置，避免操作过快
- 测试时使用自己的账号以避免骚扰他人
- 可考虑添加异常处理机制提升健壮性
- 建议添加日志记录功能便于调试