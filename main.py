from DrissionPage import ChromiumPage,ChromiumOptions
import time

def find_dm_ele_click(time_out,page):
    time.sleep(time_out)

    # 查找私信按钮
    dm_ele = page.ele('tag:span@@class=semi-button-content@@text()=私信')

    if dm_ele:
        print(f"找到私信按钮: {dm_ele.text}")
        # 使用JavaScript点击，避免位置问题
        dm_ele.click(by_js=True)
        time.sleep(1)
        dm_ele.click(by_js=True)
        print("私信按钮点击成功！")
        return True
    else:
        print("未找到私信按钮")
        return False

def find_dm_input_click(time_out,page,message,send_interval):
    time.sleep(time_out)
    # 查找发送消息输入框
    dm_input = page.ele('text:发送消息')
    if dm_input:
        print(f"找到发送消息输入框: {dm_input.text}")
        # 使用JavaScript点击，避免位置问题
        dm_input.click(by_js=True)
        time.sleep(1)
        dm_input.click(by_js=True)
        print("发送消息输入框点击成功！")
        #输入消息
        dm_input.input(message)
        time.sleep(send_interval)
        return True
    else:
        print("未找到发送消息输入框")
        return False

def Chrom_sets():
    #设置浏览器选项
    co = ChromiumOptions()
    #设置无图模式
    # co.no_imgs(True)
    #设置无头模式
    co.headless(False)
    page = ChromiumPage(co)
    return page

def base_sets():
    #设置打开主页延迟
    time_out = 5
    #发送间隔
    send_interval = 1
    #设置sec_uid
    sec_uid = 'MS4wLjABAAAAA5KVTD27RglZ7ZrTpb0cRz8qu2HVX-lyjAKKcSwBXHF-yCgklGtrPkGMwNiJ254x'
    #要私信的消息
    message = "你好！"
    #给消息加上回车
    message += '\n'
    return time_out,send_interval,sec_uid,message


def main():
    #基础设置
    time_out,send_interval,sec_uid,message = base_sets()

    #浏览器设置
    page = Chrom_sets()

    zhuye_url = f'https://www.douyin.com/user/{sec_uid}?from_tab_name=main'
    page.get(zhuye_url)
    print(page.title)

    # 等待页面加载 时间太长 改为time.sleep
    # page.wait.load_start()
    dm_ele_click = find_dm_ele_click(time_out,page)
    if not dm_ele_click:
        return
    time.sleep(time_out)

    dm_input_click = find_dm_input_click(time_out,page,message,send_interval)
    if not dm_input_click:
        return
    # page.close()

if __name__ == '__main__':
    main()